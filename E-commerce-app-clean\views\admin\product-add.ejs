<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Product</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.css">
    <link rel="stylesheet" href="/css/admin.css">
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        .error-message {
            color: #dc3545;
            font-size: 0.875em;
            margin-top: 0.25rem;
        }
        .image-preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .image-preview {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            position: relative;
        }
        .image-preview .remove-btn {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
        }
        .crop-modal .modal-body {
            padding: 0;
        }
        .crop-container {
            max-height: 400px;
            overflow: hidden;
        }
        .crop-container img {
            max-width: 100%;
            height: auto;
        }
        .crop-controls {
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        .cropped-images-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .cropped-image-item {
            position: relative;
            display: inline-block;
        }
/* Style the container */
.mb-4 {
  margin-bottom: 16px;
}

/* Style the checkbox */
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #007bff;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  position: relative;
}

input[type="checkbox"]:checked {
  background-color: #007bff;
}

input[type="checkbox"]:checked::before {
  content: '\2713'; /* Unicode checkmark */
  font-size: 16px;
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Style the label */
label {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-left: 8px;
  cursor: pointer;
}

/* Style the error message */
.error-message {
  color: red;
  font-size: 14px;
  margin-top: 4px;
  display: none; /* Initially hidden */
}

/* Show error message when there's an error */
input[type="checkbox"]:invalid ~ .error-message {
  display: block;
}

/* Validation feedback styles */
.form-control.is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid {
  border-color: #198754;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.17z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

    </style>
</head>
<body>
    <!-- Include Admin Sidebar -->
    <%- include('partials/sidebar', { page: 'products' }) %>

    <div class="main-content">
        <div class="container py-5">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h2 class="h4 mb-0"><i class="fas fa-plus-circle me-2"></i>Add New Product</h2>
                        </div>
                    <div class="card-body">
                        <% if (typeof error !== 'undefined' && error) { %>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <%= error.replace(/\+/g, ' ') %>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <% } %>

                        <form method="POST" action="/admin/add-products" enctype="multipart/form-data" id="productForm" novalidate>
                            <div class="mb-4">
                                <label for="productName" class="form-label">Product Name</label>
                                <input type="text" class="form-control" id="productName" name="productName"
                                       placeholder="Enter product name" required>
                                <div id="productName-error" class="error-message"></div>
                            </div>

                            <div class="mb-4">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description"
                                          rows="4" placeholder="Enter product description" required></textarea>
                                <div id="description-error" class="error-message"></div>
                            </div>

                            <div class="mb-4">
  <label for="isFeatured">Featured:</label>
<input type="checkbox" id="isFeatured" name="isFeatured" >

  <!-- No 'required' attribute = Optional -->
  <div id="featured-error" class="error-message"></div>
</div>


                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="price" class="form-label">Regular Price</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₹</span>
                                        <input type="number" class="form-control" id="price" name="price"
                                               step="0.01" min="0" placeholder="0.00" required>
                                    </div>
                                    <div id="price-error" class="error-message"></div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="salePrice" class="form-label">Sale Price (Optional)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₹</span>
                                        <input type="number" class="form-control" id="salePrice" name="salePrice"
                                               step="0.01" min="0" placeholder="0.00">
                                    </div>
                                    <div id="salePrice-error" class="error-message"></div>
                                    <small class="text-muted">Leave empty to auto-calculate from discount, or enter manually</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <label for="discount" class="form-label">Discount Percentage (Optional)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="discount" name="discount"
                                               min="0" max="100" placeholder="0">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <div id="discount-error" class="error-message"></div>
                                    <small class="text-muted">Enter 0-100 for discount percentage</small>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <label for="quantity" class="form-label">Quantity</label>
                                    <input type="number" class="form-control" id="quantity" name="quantity"
                                           min="0" placeholder="0" required>
                                    <div id="quantity-error" class="error-message"></div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="" selected disabled>Select category</option>
                                    <% if (cat && cat.length > 0) { %>
                                        <% cat.forEach(function(category) { %>
                                            <option value="<%= category.name %>"><%= category.name %></option>
                                        <% }); %>
                                    <% } %>
                                </select>
                                <div id="category-error" class="error-message"></div>
                            </div>

                            <div class="mb-4">
                                <label for="images" class="form-label">Product Images (At least 3 images required)</label>
                                <input class="form-control" type="file" id="images" name="images"
                                       multiple accept="image/jpeg,image/png,image/jpg">
                                <small class="form-text text-muted">Select images to crop and add. Only JPG and PNG formats are allowed.</small>

                                <!-- Cropped Images Display -->
                                <div class="cropped-images-container mt-3" id="croppedImagesContainer">
                                    <div class="text-muted" id="noImagesText">No images added yet. Please select and crop at least 3 images.</div>
                                </div>

                                <!-- Hidden input to store cropped image data -->
                                <input type="hidden" id="croppedImagesData" name="croppedImagesData">
                                <div id="images-error" class="error-message"></div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/admin/products" class="btn btn-secondary me-md-2">Cancel</a>
                                <button type="submit" class="btn btn-primary">Add Product</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Image Crop Modal -->
    <div class="modal fade crop-modal" id="cropModal" tabindex="-1" aria-labelledby="cropModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cropModalLabel">
                        <i class="fas fa-crop me-2"></i>Crop Image
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="crop-container">
                        <img id="cropImage" style="max-width: 100%;">
                    </div>
                </div>
                <div class="crop-controls">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="rotateCropper(-90)">
                                <i class="fas fa-undo"></i> Rotate Left
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="rotateCropper(90)">
                                <i class="fas fa-redo"></i> Rotate Right
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetCropper()">
                                <i class="fas fa-refresh"></i> Reset
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="cropAndSave()">
                                <i class="fas fa-check me-1"></i>Crop & Add
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        let cropper = null;
        let currentFile = null;
        let currentFileIndex = 0;
        let filesToProcess = [];
        let croppedImages = [];

        // Image selection and cropping
        document.getElementById('images').addEventListener('change', function(e) {
            const files = Array.from(this.files);
            const errorElement = document.getElementById('images-error');

            if (files.length === 0) {
                return;
            }

            // Validate file types
            const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
            for (let file of files) {
                if (!allowedTypes.includes(file.type)) {
                    errorElement.textContent = 'Only JPG and PNG images are allowed';
                    return;
                }
            }

            errorElement.textContent = '';
            filesToProcess = files;
            currentFileIndex = 0;
            processNextImage();
        });

        function processNextImage() {
            if (currentFileIndex >= filesToProcess.length) {
                // All images processed
                filesToProcess = [];
                document.getElementById('images').value = '';
                updateCroppedImagesDisplay();
                return;
            }

            currentFile = filesToProcess[currentFileIndex];
            const reader = new FileReader();
            reader.onload = function(e) {
                const cropImage = document.getElementById('cropImage');
                cropImage.src = e.target.result;

                // Show crop modal
                const cropModal = new bootstrap.Modal(document.getElementById('cropModal'));
                cropModal.show();

                // Initialize cropper when modal is shown
                document.getElementById('cropModal').addEventListener('shown.bs.modal', function() {
                    if (cropper) {
                        cropper.destroy();
                    }
                    cropper = new Cropper(cropImage, {
                        aspectRatio: 1,
                        viewMode: 1,
                        autoCropArea: 0.8,
                        responsive: true,
                        restore: false,
                        guides: true,
                        center: true,
                        highlight: false,
                        cropBoxMovable: true,
                        cropBoxResizable: true,
                        toggleDragModeOnDblclick: false,
                    });
                }, { once: true });
            };
            reader.readAsDataURL(currentFile);
        }

        // Cropper control functions
        function rotateCropper(degree) {
            if (cropper) {
                cropper.rotate(degree);
            }
        }

        function resetCropper() {
            if (cropper) {
                cropper.reset();
            }
        }

        function cropAndSave() {
            if (!cropper) return;

            const canvas = cropper.getCroppedCanvas({
                width: 800,
                height: 800,
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high',
            });

            canvas.toBlob(function(blob) {
                const croppedImage = {
                    blob: blob,
                    dataUrl: canvas.toDataURL('image/jpeg', 0.9),
                    originalName: currentFile.name,
                    index: croppedImages.length
                };

                croppedImages.push(croppedImage);
                currentFileIndex++;

                // Hide modal and destroy cropper
                const cropModal = bootstrap.Modal.getInstance(document.getElementById('cropModal'));
                cropModal.hide();

                if (cropper) {
                    cropper.destroy();
                    cropper = null;
                }

                // Process next image or finish
                setTimeout(() => {
                    processNextImage();
                }, 300);
            }, 'image/jpeg', 0.9);
        }

        function updateCroppedImagesDisplay() {
            const container = document.getElementById('croppedImagesContainer');
            const noImagesText = document.getElementById('noImagesText');

            if (croppedImages.length === 0) {
                noImagesText.style.display = 'block';
                container.innerHTML = '<div class="text-muted" id="noImagesText">No images added yet. Please select and crop at least 3 images.</div>';
                document.getElementById('croppedImagesData').value = '';
                return;
            }

            noImagesText.style.display = 'none';
            container.innerHTML = '';

            croppedImages.forEach((image, index) => {
                const imageItem = document.createElement('div');
                imageItem.className = 'cropped-image-item';
                imageItem.innerHTML = `
                    <img src="${image.dataUrl}" class="image-preview" alt="Cropped image ${index + 1}">
                    <button type="button" class="remove-btn" onclick="removeCroppedImage(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(imageItem);
            });

            // Update hidden input with cropped images data
            const imageDataArray = croppedImages.map(img => img.dataUrl);
            document.getElementById('croppedImagesData').value = JSON.stringify(imageDataArray);
        }

        function removeCroppedImage(index) {
            croppedImages.splice(index, 1);
            // Update indices
            croppedImages.forEach((img, i) => {
                img.index = i;
            });
            updateCroppedImagesDisplay();
        }

        // Auto-calculate sale price based on regular price and discount
        function calculateSalePrice() {
            const priceInput = document.getElementById('price');
            const salePriceInput = document.getElementById('salePrice');
            const discountInput = document.getElementById('discount');

            const regularPrice = parseFloat(priceInput.value) || 0;
            const discount = parseFloat(discountInput.value) || 0;
            const currentSalePrice = salePriceInput.value.trim();

            // Only calculate if salePrice is empty and both regularPrice and discount are provided
            if (!currentSalePrice && regularPrice > 0 && discount > 0) {
                const calculatedSalePrice = regularPrice - (regularPrice * discount / 100);
                salePriceInput.value = calculatedSalePrice.toFixed(2);

                // Add visual feedback
                salePriceInput.style.backgroundColor = '#e8f5e8';
                salePriceInput.style.border = '2px solid #28a745';

                // Show a small notification
                const salePriceContainer = salePriceInput.closest('.col-md-6');
                let notification = salePriceContainer.querySelector('.auto-calc-notification');
                if (!notification) {
                    notification = document.createElement('small');
                    notification.className = 'auto-calc-notification text-success mt-1 d-block';
                    salePriceContainer.appendChild(notification);
                }
                notification.innerHTML = '<i class="fas fa-check-circle me-1"></i>Auto-calculated from discount';

                // Remove visual feedback after 3 seconds
                setTimeout(() => {
                    salePriceInput.style.backgroundColor = '';
                    salePriceInput.style.border = '';
                    if (notification) {
                        notification.remove();
                    }
                }, 3000);
            }
        }

        // Add event listeners for auto-calculation
        document.addEventListener('DOMContentLoaded', function() {
            const priceInput = document.getElementById('price');
            const discountInput = document.getElementById('discount');
            const salePriceInput = document.getElementById('salePrice');

            // Calculate when price or discount changes
            priceInput.addEventListener('input', calculateSalePrice);
            discountInput.addEventListener('input', calculateSalePrice);

            // Clear auto-calculation when user manually enters sale price
            salePriceInput.addEventListener('input', function() {
                if (this.value.trim()) {
                    this.style.backgroundColor = '';
                    this.style.border = '';
                    const notification = this.closest('.col-md-6').querySelector('.auto-calc-notification');
                    if (notification) {
                        notification.remove();
                    }
                }
            });
        });

        // Enhanced form validation with SweetAlert
        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault(); // Always prevent default to handle validation first

            // Auto-calculate sale price before validation if needed
            calculateSalePrice();

            // Clear previous errors
            const errorElements = document.querySelectorAll('.error-message');
            errorElements.forEach(el => el.textContent = '');

            // Validate all fields and collect errors
            const validationErrors = await validateAllFields();

            // If validation passes, show loading and submit
            if (validationErrors.length === 0) {
                // Show loading state
                Swal.fire({
                    title: 'Adding Product...',
                    html: 'Please wait while we add your product.',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Submit the form
                this.submit();
            } else {
                // Show validation errors with SweetAlert
                await showValidationErrors(validationErrors);
            }
        });

        // Comprehensive validation function
        async function validateAllFields() {
            const errors = [];

            // Product name validation
            const productName = document.getElementById('productName');
            const productNameVal = productName.value.trim();

            if (!productNameVal) {
                errors.push({ field: 'Product Name', message: 'Product name is required' });
            } else if (productNameVal.length < 2) {
                errors.push({ field: 'Product Name', message: 'Product name must be at least 2 characters' });
            } else if (productNameVal.length > 50) {
                errors.push({ field: 'Product Name', message: 'Product name must be less than 50 characters' });
            } else if (/^\s+$/.test(productNameVal)) {
                errors.push({ field: 'Product Name', message: 'Product name cannot contain only spaces' });
            } else if (!/^[A-Za-z0-9\s-]+$/.test(productNameVal)) {
                errors.push({ field: 'Product Name', message: 'Product name can only contain letters, numbers, spaces, and hyphens' });
            } else if (!/^[A-Za-z]/.test(productNameVal)) {
                errors.push({ field: 'Product Name', message: 'Product name must start with a letter' });
            } else if (/\s{2,}/.test(productNameVal)) {
                errors.push({ field: 'Product Name', message: 'Product name cannot have multiple consecutive spaces' });
            } else if (productNameVal !== productNameVal.trim()) {
                errors.push({ field: 'Product Name', message: 'Product name cannot start or end with spaces' });
            } else {
                // Check for duplicate product names via AJAX
                try {
                    const response = await fetch(`/admin/check-product-name?name=${encodeURIComponent(productNameVal)}`);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.exists) {
                            errors.push({ field: 'Product Name', message: 'A product with this name already exists' });
                        }
                    }
                } catch (error) {
                    console.log('Could not check for duplicate product names:', error);
                }
            }

            // Description validation
            const description = document.getElementById('description');
            const descriptionVal = description.value.trim();
            if (!descriptionVal) {
                errors.push({ field: 'Description', message: 'Description is required' });
            } else if (descriptionVal.length < 10) {
                errors.push({ field: 'Description', message: 'Description must be at least 10 characters long' });
            } else if (descriptionVal.length > 500) {
                errors.push({ field: 'Description', message: 'Description must be less than 500 characters' });
            } else if (/^\s+$/.test(descriptionVal)) {
                errors.push({ field: 'Description', message: 'Description cannot contain only spaces' });
            }

            // Price validation
            const price = document.getElementById('price');
            const priceVal = price.value.trim();
            if (!priceVal) {
                errors.push({ field: 'Price', message: 'Price is required' });
            } else if (isNaN(priceVal) || parseFloat(priceVal) <= 0) {
                errors.push({ field: 'Price', message: 'Price must be a valid positive number' });
            } else if (parseFloat(priceVal) > 1000000) {
                errors.push({ field: 'Price', message: 'Price cannot exceed ₹10,00,000' });
            }

            // Sale price validation
            const salePrice = document.getElementById('salePrice');
            const salePriceVal = salePrice.value.trim();
            if (salePriceVal) {
                if (isNaN(salePriceVal) || parseFloat(salePriceVal) < 0) {
                    errors.push({ field: 'Sale Price', message: 'Sale price must be a valid positive number' });
                } else if (priceVal && parseFloat(salePriceVal) >= parseFloat(priceVal)) {
                    errors.push({ field: 'Sale Price', message: 'Sale price must be less than regular price' });
                }
            }

            // Discount validation
            const discount = document.getElementById('discount');
            const discountVal = discount.value.trim();
            if (discountVal) {
                if (isNaN(discountVal) || parseFloat(discountVal) < 0 || parseFloat(discountVal) > 100) {
                    errors.push({ field: 'Discount', message: 'Discount must be between 0 and 100' });
                }
            }

            // Quantity validation
            const quantity = document.getElementById('quantity');
            const quantityVal = quantity.value.trim();
            if (!quantityVal) {
                errors.push({ field: 'Quantity', message: 'Quantity is required' });
            } else if (isNaN(quantityVal) || parseInt(quantityVal) < 0 || !Number.isInteger(Number(quantityVal))) {
                errors.push({ field: 'Quantity', message: 'Quantity must be a valid non-negative integer' });
            } else if (parseInt(quantityVal) > 10000) {
                errors.push({ field: 'Quantity', message: 'Quantity cannot exceed 10,000' });
            }

            // Category validation
            const category = document.getElementById('category');
            if (!category.value) {
                errors.push({ field: 'Category', message: 'Category is required' });
            }

            // Images validation
            if (croppedImages.length < 3) {
                errors.push({ field: 'Images', message: 'Please crop and add at least 3 images' });
            } else if (croppedImages.length > 4) {
                errors.push({ field: 'Images', message: 'Maximum 4 images are allowed' });
            }

            return errors;
        }

        // Function to show validation errors with SweetAlert
        async function showValidationErrors(errors) {
            if (errors.length === 1) {
                // Single error - show simple alert
                await Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    text: `${errors[0].field}: ${errors[0].message}`,
                    confirmButtonColor: '#dc3545',
                    confirmButtonText: 'Fix Issue'
                });
            } else {
                // Multiple errors - show detailed list
                const errorList = errors.map(error => `• <strong>${error.field}:</strong> ${error.message}`).join('<br>');
                await Swal.fire({
                    icon: 'error',
                    title: 'Multiple Validation Errors',
                    html: `Please fix the following issues:<br><br>${errorList}`,
                    confirmButtonColor: '#dc3545',
                    confirmButtonText: 'Fix Issues',
                    width: '600px'
                });
            }

            // Focus on the first error field
            if (errors.length > 0) {
                const firstErrorField = errors[0].field.toLowerCase().replace(' ', '');
                const fieldMap = {
                    'productname': 'productName',
                    'description': 'description',
                    'price': 'price',
                    'saleprice': 'salePrice',
                    'discount': 'discount',
                    'quantity': 'quantity',
                    'category': 'category'
                };

                const fieldId = fieldMap[firstErrorField];
                if (fieldId) {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.focus();
                        field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }
            }
        }

// Real-time validation for product name
document.addEventListener('DOMContentLoaded', function() {
    const productNameInput = document.getElementById('productName');
    const productNameError = document.getElementById('productName-error');

    // Real-time validation for product name
    productNameInput.addEventListener('input', function() {
        const value = this.value;
        const trimmedValue = value.trim();

        // Reset styles
        this.classList.remove('is-invalid', 'is-valid');
        productNameError.textContent = '';

        if (value.length === 0) {
            return; // Don't show validation for empty field while typing
        }

        let isValid = true;
        let errorMessage = '';

        // Check various validation rules
        if (trimmedValue.length < 2) {
            isValid = false;
            errorMessage = 'Must be at least 2 characters';
        } else if (trimmedValue.length > 50) {
            isValid = false;
            errorMessage = 'Must be less than 50 characters';
        } else if (/^\s+$/.test(value)) {
            isValid = false;
            errorMessage = 'Cannot contain only spaces';
        } else if (!/^[A-Za-z0-9\s-]*$/.test(trimmedValue)) {
            isValid = false;
            errorMessage = 'Only letters, numbers, spaces, and hyphens allowed';
        } else if (!/^[A-Za-z]/.test(trimmedValue)) {
            isValid = false;
            errorMessage = 'Must start with a letter';
        } else if (/\s{2,}/.test(trimmedValue)) {
            isValid = false;
            errorMessage = 'Cannot have multiple consecutive spaces';
        } else {
            // Check for duplicates if we have existing product data
            const existingProductNames = window.existingProducts || [];
            if (existingProductNames.some(name => name.toLowerCase() === trimmedValue.toLowerCase())) {
                isValid = false;
                errorMessage = 'Product name already exists';
            }
        }

        // Apply validation feedback
        if (isValid) {
            this.classList.add('is-valid');
        } else {
            this.classList.add('is-invalid');
            productNameError.textContent = errorMessage;
        }
    });

    // Debounced AJAX check for duplicate product names
    let debounceTimer;
    productNameInput.addEventListener('input', function() {
        const value = this.value.trim();

        if (value.length < 2) return;

        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(async () => {
            try {
                const response = await fetch(`/admin/check-product-name?name=${encodeURIComponent(value)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.exists) {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                        productNameError.textContent = 'Product name already exists';
                    } else if (this.classList.contains('is-invalid') && productNameError.textContent === 'Product name already exists') {
                        // Clear the duplicate error if it was the only issue
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                        productNameError.textContent = '';
                    }
                }
            } catch (error) {
                console.log('Could not check for duplicate product names:', error);
                // Fail silently for duplicate check
            }
        }, 500);
    });
});
</script>

</body>
</html>