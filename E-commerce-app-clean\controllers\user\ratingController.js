import { Product } from "../../model/productModel.js";
import { Review } from "../../model/reviewModel.js";
import { catchAsyncError } from "../../middlewares/catchAsync.js";
import <PERSON>rror<PERSON>and<PERSON> from "../../middlewares/error.js";
import HttpStatus from "../../helpers/httpStatus.js";

// Rate a product (1-5 stars)
export const rateProduct = catchAsyncError(async (req, res, next) => {
  try {
    const { id: productId } = req.params;
    const { rating } = req.body;
    const userId = req.user._id;

    // Validate input
    if (!rating || rating < 1 || rating > 5 || !Number.isInteger(Number(rating))) {
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Rating must be an integer between 1 and 5'
      });
    }

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product is available
    if (product.isBlocked || product.isDeleted || product.status !== "Available") {
      return res.status(HttpStatus.BAD_REQUEST).json({
        success: false,
        message: 'This product is currently not available for rating'
      });
    }

    // Check if user already rated this product
    const existingRatingIndex = product.ratings.findIndex(
      r => r.userId.toString() === userId.toString()
    );

    if (existingRatingIndex !== -1) {
      // Update existing rating
      product.ratings[existingRatingIndex].rating = parseInt(rating);
      product.ratings[existingRatingIndex].createdAt = new Date();
    } else {
      // Add new rating
      product.ratings.push({
        userId: userId,
        rating: parseInt(rating),
        createdAt: new Date()
      });
    }

    await product.save();

    // Update product rating statistics
    await Product.updateProductRating(productId);

    // Get updated product for response
    const updatedProduct = await Product.findById(productId);

    res.status(HttpStatus.OK).json({
      success: true,
      message: existingRatingIndex !== -1 ? 'Rating updated successfully' : 'Rating added successfully',
      data: {
        productId: productId,
        userRating: parseInt(rating),
        averageRating: updatedProduct.averageRating,
        ratingCount: updatedProduct.ratingCount,
        reviewCount: updatedProduct.reviewCount
      }
    });

  } catch (error) {
    console.error('Error rating product:', error);
    return next(new ErrorHandler('Failed to rate product', HttpStatus.INTERNAL_SERVER_ERROR));
  }
});

// Update product rating (recalculate from reviews and direct ratings)
export const updateProductRating = catchAsyncError(async (req, res, next) => {
  try {
    const { id: productId } = req.params;

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Update product rating
    const updatedProduct = await Product.updateProductRating(productId);

    if (!updatedProduct) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to update product rating'
      });
    }

    res.status(HttpStatus.OK).json({
      success: true,
      message: 'Product rating updated successfully',
      data: {
        productId: productId,
        averageRating: updatedProduct.averageRating,
        ratingCount: updatedProduct.ratingCount,
        reviewCount: updatedProduct.reviewCount
      }
    });

  } catch (error) {
    console.error('Error updating product rating:', error);
    return next(new ErrorHandler('Failed to update product rating', HttpStatus.INTERNAL_SERVER_ERROR));
  }
});

// Get user's rating for a product
export const getUserRating = catchAsyncError(async (req, res, next) => {
  try {
    const { id: productId } = req.params;
    const userId = req.user._id;

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Find user's rating
    const userRating = product.ratings.find(
      r => r.userId.toString() === userId.toString()
    );

    res.status(HttpStatus.OK).json({
      success: true,
      data: {
        productId: productId,
        userRating: userRating ? userRating.rating : null,
        hasRated: !!userRating,
        ratedAt: userRating ? userRating.createdAt : null
      }
    });

  } catch (error) {
    console.error('Error getting user rating:', error);
    return next(new ErrorHandler('Failed to get user rating', HttpStatus.INTERNAL_SERVER_ERROR));
  }
});

// Get product rating statistics
export const getProductRatingStats = catchAsyncError(async (req, res, next) => {
  try {
    const { id: productId } = req.params;

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Get rating distribution from reviews
    const reviewRatingDistribution = await Review.aggregate([
      { $match: { product: product._id, isApproved: true } },
      { $group: { _id: '$rating', count: { $sum: 1 } } },
      { $sort: { _id: -1 } }
    ]);

    // Get rating distribution from direct ratings
    const directRatingDistribution = {};
    product.ratings.forEach(rating => {
      directRatingDistribution[rating.rating] = (directRatingDistribution[rating.rating] || 0) + 1;
    });

    // Combine distributions
    const combinedDistribution = {};
    for (let i = 1; i <= 5; i++) {
      const reviewCount = reviewRatingDistribution.find(r => r._id === i)?.count || 0;
      const directCount = directRatingDistribution[i] || 0;
      combinedDistribution[i] = reviewCount + directCount;
    }

    res.status(HttpStatus.OK).json({
      success: true,
      data: {
        productId: productId,
        averageRating: product.averageRating,
        ratingCount: product.ratingCount,
        reviewCount: product.reviewCount,
        ratingDistribution: combinedDistribution
      }
    });

  } catch (error) {
    console.error('Error getting product rating stats:', error);
    return next(new ErrorHandler('Failed to get product rating statistics', HttpStatus.INTERNAL_SERVER_ERROR));
  }
});
