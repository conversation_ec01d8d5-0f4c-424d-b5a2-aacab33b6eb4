
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Luxe Scents | Premium Perfume Store</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <style>
    .hero-section {
      background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('http://localhost:4003/assets/HomeCover%20Image%20perfume.jpg');
      background-size: cover;
      background-position: center;
      height: 75vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      text-align: center;
      margin-bottom: 30px;
    }
    .hero-content h1 {
      font-size: 3.5rem;
      font-weight: 700;
    }
    .hero-content p {
      font-size: 1.2rem;
      max-width: 700px;
      margin: 0 auto 20px;
    }
    .navbar {
      background-color: #000000;
      padding: 1rem 2rem;
      border-bottom: 1px solid #090909;
    }
    .navbar-brand {
      font-weight: 700;
      font-size: 1.8rem;
      color: #ffffff !important;
      letter-spacing: 1px;
    }
    .navbar-brand:hover {
      color: #bb86fc !important;
    }
    .nav-link {
      color: #e0e0e0 !important;
      font-weight: 500;
      margin: 0 1rem;
      transition: color 0.3s;
    }
    .nav-link:hover {
      color: #bb86fc !important;
    }
    .navbar-icons {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }
    .navbar-icons a {
      color: #e0e0e0;
      font-size: 1.2rem;
      position: relative;
      transition: color 0.3s;
    }
    .navbar-icons a:hover {
      color: #bb86fc;
    }
    .cart-count {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #6200ea;
      color: #ffffff;
      font-size: 0.7rem;
      border-radius: 50%;
      padding: 2px 6px;
    }

    /* Rating Styles */
    .product-rating {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;
    }

    .stars {
      display: flex;
      gap: 1px;
    }

    .star {
      font-size: 1rem;
      color: #ddd;
    }

    .star.filled {
      color: #ffc107;
    }

    .star.half {
      color: #ffc107;
      opacity: 0.7;
    }

    .star.empty {
      color: #ddd;
    }

    .rating-text {
      color: #666;
      font-size: 0.8rem;
    }
    .dropdown-menu {
      background-color: #2a2a2a;
      border: 1px solid #444;
    }
    .dropdown-item {
      color: #e0e0e0 !important;
      transition: background-color 0.3s, color 0.3s;
    }
    .dropdown-item:hover {
      background-color: #6200ea;
      color: #ffffff !important;
    }
    footer {
      background-color: #000000;
      color: #e0e0e0;
      padding: 3rem 2rem;
      margin-top: 2rem;
      border-top: 1px solid #333;
    }
    footer .container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 2rem;
    }
    footer h5 {
      color: #ffffff;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }
    footer ul {
      list-style: none;
      padding: 0;
    }
    footer ul li {
      margin-bottom: 0.5rem;
    }
    footer ul li a {
      color: #b0b0b0;
      text-decoration: none;
      transition: color 0.3s;
    }
    footer ul li a:hover {
      color: #bb86fc;
    }
    footer .social-icons a {
      color: #e0e0e0;
      font-size: 1.5rem;
      margin-right: 1rem;
      transition: color 0.3s;
    }
    footer .social-icons a:hover {
      color: #bb86fc;
    }
    footer .footer-bottom {
      text-align: center;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #333;
      color: #b0b0b0;
      font-size: 0.9rem;
    }
    .pagination .page-link {
      color: #6200ea;
      background-color: #ffffff;
      border: 1px solid #6200ea;
      margin: 0 0.2rem;
      transition: background-color 0.3s, color 0.3s;
    }
    .pagination .page-link:hover {
      background-color: #6200ea;
      color: #ffffff;
      border-color: #6200ea;
    }
    .pagination .page-item.active .page-link {
      background-color: #6200ea;
      color: #ffffff;
      border-color: #6200ea;
    }
    .pagination .page-item.disabled .page-link {
      color: #b0b0b0;
      border-color: #b0b0b0;
      background-color: #f9f9f9;
      cursor: not-allowed;
    }
    @media (max-width: 768px) {
      footer .container {
        flex-direction: column;
        text-align: center;
      }
      footer .social-icons {
        justify-content: center;
      }
      .navbar-icons {
        gap: 1rem;
      }
      .pagination {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
      <a class="navbar-brand" href="/">LUXE SCENTS</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link active" href="/">Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/shop">Shop</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">Contact</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#">About</a>
          </li>
        </ul>
        <div class="navbar-icons">
          <a href="/wishlist" class="position-relative">
            <i class="fas fa-heart"></i>
            <span class="wishlist-count" id="wishlistCount">0</span>
          </a>
          <a href="/cart" class="position-relative">
            <i class="fas fa-shopping-cart"></i>
            <span class="cart-count" id="cartCount">0</span>
          </a>
          <div class="dropdown">
            <a href="#" class="dropdown-toggle" role="button" data-bs-toggle="dropdown">
              <i class="fas fa-user-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li><a class="dropdown-item" href="/profile">My Profile</a></li>
              <li><a class="dropdown-item" href="/addresses">My Addresses</a></li>
              <li><hr class="dropdown-divider" style="border-color: #444;"></li>
              <li><a class="dropdown-item" href="#" onclick="confirmLogout(event)">Logout</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Search Form -->
  <section class="search-section" style="padding: 2rem 0;">
    <div class="container">
      <form id="searchForm" action="/shop" method="GET">
        <div class="input-group">
          <input type="text" id="searchInput" name="search" class="form-control" placeholder="Search products...">
          <select name="category" id="categorySelect" class="form-select">
            <option value="">All Categories</option>
            <% categories.forEach(category => { %>
              <option value="<%= category.name %>"><%= category.name %></option>
            <% }) %>
          </select>
          <button type="submit" id="searchButton" class="btn btn-primary" style="background-color: #6200ea; border-color: #6200ea;" disabled>Search</button>
        </div>
        <div id="searchError" class="text-danger small mt-2" style="display: none;"></div>
      </form>
    </div>
  </section>

  <section class="hero-section">
    <div class="hero-content">
      <h1>Premium Perfumes for Every Occasion</h1>
      <p>Discover our exclusive collection of luxury fragrances</p>
      <a href="/shop" class="btn btn-primary btn-lg">Shop Now</a>
    </div>
  </section>

  <!-- Featured Products Section -->
  <section class="featured-section" style="background-color: #f9f9f9; padding: 4rem 0;">
    <div class="container">
      <div class="section-title" style="text-align: center; margin-bottom: 2rem; position: relative;">
        <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">Featured Products</h2>
        <p>Our most popular and exclusive fragrances</p>
        <div style="content: ''; display: block; width: 80px; height: 3px; background-color: #6200ea; margin: 1rem auto;"></div>
      </div>

      <div class="row" id="featuredProductGrid">
        <% if (featuredProducts && featuredProducts.length > 0) { %>
          <% featuredProducts.forEach(product => { %>
            <div class="col-md-3">
              <div class="card product-card" style="border: none; box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: transform 0.3s, box-shadow 0.3s; margin-bottom: 1.5rem; position: relative;">
                <!-- Wishlist Button -->
                <button class="btn btn-outline-danger wishlist-btn" onclick="addToWishlist('<%= product._id %>')" style="position: absolute; top: 10px; right: 10px; z-index: 10; border-radius: 50%; width: 40px; height: 40px; padding: 0; background: rgba(255,255,255,0.9); border: 1px solid #dc3545;">
                  <i class="fas fa-heart" style="color: #dc3545;"></i>
                </button>

                <a href="/product/<%= product._id %>" class="text-decoration-none">
                  <div class="product-thumb" style="height: 200px; overflow: hidden;">
                    <% if (product.productImage && product.productImage.length > 0) { %>
                      <img src="/Uploads/product-images/<%= product.productImage[0] %>" alt="<%= product.productName %>" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s;" />
                    <% } else { %>
                      <img src="/images/default-product.jpg" alt="No image" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s;" />
                    <% } %>
                  </div>
                  <div class="product-info" style="padding: 1.5rem;">
                    <h5 class="product-title" style="font-weight: 600; margin-bottom: 0.5rem; color: #333;"><%= product.productName %></h5>
                    <p class="product-category text-muted" style="font-size: 0.9rem; margin-bottom: 0.5rem;"><%= product.category ? product.category.name : "Uncategorized" %></p>

                     <!-- Rating Display -->
                      <div class="product-rating mb-2">
                        <div class="stars">
                          <%
                            const avgRating = product.averageRating || 0;
                            const fullStars = Math.floor(avgRating);
                            const hasHalfStar = (avgRating % 1) >= 0.5;
                            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
                          %>
                          <% for(let i = 0; i < fullStars; i++) { %>
                            <span class="star filled">★</span>
                          <% } %>
                          <% if(hasHalfStar) { %>
                            <span class="star half">★</span>
                          <% } %>
                          <% for(let i = 0; i < emptyStars; i++) { %>
                            <span class="star empty">☆</span>
                          <% } %>
                        </div>
                        <span class="rating-text">
                          <% if(product.ratingCount > 0) { %>
                            (<%= avgRating.toFixed(1) %>) <%= product.ratingCount %> rating<%= product.ratingCount !== 1 ? 's' : '' %>
                          <% } else { %>
                            No ratings yet
                          <% } %>
                        </span>
                      </div>

                    <div class="price-section">
                      <% if (product.salePrice && product.salePrice < product.price) { %>
                        <div class="d-flex align-items-center flex-wrap">
                          <span class="sale-price text-success fw-bold fs-5">₹<%= product.salePrice.toFixed(2) %></span>
                          <span class="original-price text-muted text-decoration-line-through ms-2" style="font-size: 0.9rem;">₹<%= product.price.toFixed(2) %></span>
                        </div>
                        <% if (product.discount > 0) { %>
                          <div class="mt-1">
                            <span class="discount-badge bg-danger text-white px-2 py-1 rounded" style="font-size: 0.7rem; font-weight: bold;">-<%= product.discount %>% OFF</span>
                          </div>
                        <% } %>
                        <div class="savings-text mt-1">
                          <small class="text-success fw-bold">Save ₹<%= (product.price - product.salePrice).toFixed(2) %></small>
                        </div>
                      <% } else { %>
                        <span class="product-price text-primary fw-bold" style="font-size: 1.2rem;">₹<%= product.price.toFixed(2) %></span>
                      <% } %>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          <% }) %>
        <% } else { %>
          <div class="col-12 text-center">
            <p>No featured products available at the moment.</p>
          </div>
        <% } %>
      </div>

      <!-- Featured Products Pagination -->
      <nav aria-label="Featured products pagination">
        <ul class="pagination justify-content-center mt-4 fragrances-section" id="featuredPagination">
          <!-- Pagination controls will be dynamically populated -->
        </ul>
      </nav>

      <div class="text-center mt-4">
        <a href="/shop" class="btn btn-primary" style="background-color: #6200ea; border-color: #6200ea; padding: 0.5rem 1.5rem; font-weight: 500; transition: background-color 0.3s;">View All Products</a>
      </div>
    </div>
  </section>

  <!-- Latest Products Section -->
  <section class="latest-section" style="padding: 4rem 0;">
    <div class="container">
      <div class="section-title" style="text-align: center; margin-bottom: 2rem; position: relative;">
        <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">Latest Arrivals</h2>
        <p>The newest additions to our collection</p>
        <div style="content: ''; display: block; width: 80px; height: 3px; background-color: #6200ea; margin: 1rem auto;"></div>
      </div>

      <div class="row" id="latestProductGrid">
        <% if (latestProducts && latestProducts.length > 0) { %>
          <% latestProducts.forEach(product => { %>
            <div class="col-md-3">
              <div class="card product-card" style="border: none; box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: transform 0.3s, box-shadow 0.3s; margin-bottom: 1.5rem; position: relative;">
                <!-- Wishlist Button -->
                <button class="btn btn-outline-danger wishlist-btn" onclick="addToWishlist('<%= product._id %>')" style="position: absolute; top: 10px; right: 10px; z-index: 10; border-radius: 50%; width: 40px; height: 40px; padding: 0; background: rgba(255,255,255,0.9); border: 1px solid #dc3545;">
                  <i class="fas fa-heart" style="color: #dc3545;"></i>
                </button>

                <a href="/product/<%= product._id %>" class="text-decoration-none">
                  <div class="product-thumb" style="height: 200px; overflow: hidden;">
                    <% if (product.productImage && product.productImage.length > 0) { %>
                      <img src="/Uploads/product-images/<%= product.productImage[0] %>" alt="<%= product.productName %>" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s;" />
                    <% } else { %>
                      <img src="/images/default-product.jpg" alt="No image" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s;" />
                    <% } %>
                  </div>
                  <div class="product-info" style="padding: 1.5rem;">
                    <h5 class="product-title" style="font-weight: 600; margin-bottom: 0.5rem; color: #333;"><%= product.productName %></h5>
                    <p class="product-category text-muted" style="font-size: 0.9rem; margin-bottom: 0.5rem;"><%= product.category ? product.category.name : "Uncategorized" %></p>
                     <!-- Rating Display -->
                      <div class="product-rating mb-2">
                        <div class="stars">
                          <%
                            const avgRating = product.averageRating || 0;
                            const fullStars = Math.floor(avgRating);
                            const hasHalfStar = (avgRating % 1) >= 0.5;
                            const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
                          %>
                          <% for(let i = 0; i < fullStars; i++) { %>
                            <span class="star filled">★</span>
                          <% } %>
                          <% if(hasHalfStar) { %>
                            <span class="star half">★</span>
                          <% } %>
                          <% for(let i = 0; i < emptyStars; i++) { %>
                            <span class="star empty">☆</span>
                          <% } %>
                        </div>
                        <span class="rating-text">
                          <% if(product.ratingCount > 0) { %>
                            (<%= avgRating.toFixed(1) %>) <%= product.ratingCount %> rating<%= product.ratingCount !== 1 ? 's' : '' %>
                          <% } else { %>
                            No ratings yet
                          <% } %>
                        </span>
                      </div>

                    <div class="price-section">
                      <% if (product.salePrice && product.salePrice < product.price) { %>
                        <div class="d-flex align-items-center flex-wrap">
                          <span class="sale-price text-success fw-bold fs-5">₹<%= product.salePrice.toFixed(2) %></span>
                          <span class="original-price text-muted text-decoration-line-through ms-2" style="font-size: 0.9rem;">₹<%= product.price.toFixed(2) %></span>
                        </div>
                        <% if (product.discount > 0) { %>
                          <div class="mt-1">
                            <span class="discount-badge bg-danger text-white px-2 py-1 rounded" style="font-size: 0.7rem; font-weight: bold;">-<%= product.discount %>% OFF</span>
                          </div>
                        <% } %>
                        <div class="savings-text mt-1">
                          <small class="text-success fw-bold">Save ₹<%= (product.price - product.salePrice).toFixed(2) %></small>
                        </div>
                      <% } else { %>
                        <span class="product-price text-primary fw-bold" style="font-size: 1.2rem;">₹<%= product.price.toFixed(2) %></span>
                      <% } %>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          <% }) %>
        <% } else { %>
          <div class="col-12 text-center">
            <p>No products available at the moment.</p>
          </div>
        <% } %>
      </div>

      <!-- Latest Products Pagination -->
      <nav aria-label="Latest products pagination">
        <ul class="pagination justify-content-center mt-4" id="latestPagination">
          <!-- Pagination controls will be dynamically populated -->
        </ul>
      </nav>

      <div class="text-center mt-4">
        <a href="/shop" class="btn btn-primary" style="background-color: #6200ea; border-color: #6200ea; padding: 0.5rem 1.5rem; font-weight: 500; transition: background-color 0.3s;">Explore All</a>
      </div>
    </div>
  </section>

  <footer>
    <div class="container">
      <div class="footer-section">
        <h5>About Luxe Scents</h5>
        <p style="color: #b0b0b0; max-width: 300px;">
          Discover the finest luxury fragrances crafted for every occasion. Elevate your senses with Luxe Scents.
        </p>
      </div>
      <div class="footer-section">
        <h5>Quick Links</h5>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/shop">Shop</a></li>
          <li><a href="#">Contact Us</a></li>
          <li><a href="#">FAQs</a></li>
        </ul>
      </div>
      <div class="footer-section">
        <h5>Contact Us</h5>
        <ul>
          <li>Email: <EMAIL></li>
          <li>Phone: +****************</li>
          <li>Address: 123 Fragrance Lane, Scent City</li>
        </ul>
      </div>
      <div class="footer-section">
        <h5>Follow Us</h5>
        <div class="social-icons">
          <a href="#"><i class="fab fa-facebook-f"></i></a>
          <a href="#"><i class="fab fa-instagram"></i></a>
          <a href="#"><i class="fab fa-twitter"></i></a>
          <a href="#"><i class="fab fa-pinterest"></i></a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>© 2025 Luxe Scents. All rights reserved.</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <!-- No Back Button Prevention Script -->
  <script src="/js/no-back-button.js"></script>
  <script>
    // Check user status periodically to handle real-time blocking
    function checkUserStatus() {
      fetch('/check-status', {
        method: 'GET',
        credentials: 'include'
      })
      .then(response => response.json())
      .then(data => {
        // Only logout if user is specifically blocked, not just unauthenticated
        if (data.blocked === true) {
          // User has been blocked, show message and redirect
          Swal.fire({
            icon: 'error',
            title: 'Account Blocked',
            text: data.message || 'Your account has been blocked. Please contact support.',
            confirmButtonColor: '#d33',
            allowOutsideClick: false,
            allowEscapeKey: false
          }).then(() => {
            // Clear any local storage/session data
            document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            window.location.href = '/login?error=Account has been blocked';
          });
        }
      })
      .catch(error => {
        console.error('Error checking user status:', error);
        // Don't logout on network errors
      });
    }

    // Check user status every 30 seconds
    setInterval(checkUserStatus, 30000);

    window.addEventListener('pageshow', (event) => {
      if (event.persisted) {
        window.location.reload();
      }
    });

    // Search validation function
    function validateSearchInput(searchValue) {
      // Allow empty search (show all products)
      if (!searchValue) {
        return { isValid: true, message: '' };
      }

      // Check if input contains only spaces (before trimming)
      if (/^\s+$/.test(searchValue)) {
        return { isValid: false, message: 'Search cannot contain only spaces' };
      }

      const trimmedValue = searchValue.trim();

      // After trimming, if empty, it's valid (same as empty search)
      if (trimmedValue === '') {
        return { isValid: true, message: '' };
      }

      // Check if input contains at least one alphabetic character
      if (!/[a-zA-Z]/.test(trimmedValue)) {
        return { isValid: false, message: 'Please enter a valid search keyword with at least one letter' };
      }

      // Check minimum length (at least 2 characters after trimming)
      if (trimmedValue.length < 2) {
        return { isValid: false, message: 'Search keyword must be at least 2 characters long' };
      }

      return { isValid: true, message: '' };
    }

    // Update search button state
    function updateSearchButtonState() {
      const searchInput = document.getElementById('searchInput');
      const searchButton = document.getElementById('searchButton');
      const searchError = document.getElementById('searchError');
      const categorySelect = document.getElementById('categorySelect');

      const searchValue = searchInput.value;
      const categoryValue = categorySelect.value;
      const validation = validateSearchInput(searchValue);

      // Show/hide error message
      if (!validation.isValid) {
        searchError.textContent = validation.message;
        searchError.style.display = 'block';
        searchInput.classList.add('is-invalid');
      } else {
        searchError.style.display = 'none';
        searchInput.classList.remove('is-invalid');
      }

      // Enable button if search is valid OR if category is selected OR if search is empty
      const shouldEnableButton = validation.isValid || categoryValue.trim() !== '' || searchValue.trim() === '';
      searchButton.disabled = !shouldEnableButton;
    }

    // Initialize pagination on page load
    document.addEventListener('DOMContentLoaded', () => {
      // Initialize search validation
      const searchForm = document.getElementById('searchForm');
      const searchInput = document.getElementById('searchInput');
      const categorySelect = document.getElementById('categorySelect');

      if (searchForm && searchInput && categorySelect) {
        // Add real-time validation
        searchInput.addEventListener('input', updateSearchButtonState);
        categorySelect.addEventListener('change', updateSearchButtonState);

        // Initial validation
        updateSearchButtonState();

        // Form submission validation
        searchForm.addEventListener('submit', function(e) {
          const searchValue = searchInput.value;
          const validation = validateSearchInput(searchValue);

          if (!validation.isValid) {
            e.preventDefault();
            Swal.fire({
              icon: 'warning',
              title: 'Invalid Search',
              text: validation.message,
              confirmButtonColor: '#6200ea'
            });
            return false;
          }

          // If valid, allow form submission
          return true;
        });
      }

      // Only initialize pagination controls, don't load content via AJAX on first load
      // The server already renders the first page correctly with sale prices
      initializePaginationControls('featured');
      initializePaginationControls('latest');

      // Update cart and wishlist counts
      updateCounts();
    });

    // Update cart and wishlist counts in header
    function updateCounts() {
      fetch('/api/cart/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const cartBadge = document.getElementById('cartCount');
            if (cartBadge) {
              cartBadge.textContent = data.cartCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating cart count:', error);
        });

      fetch('/api/wishlist/count')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const wishlistBadge = document.getElementById('wishlistCount');
            if (wishlistBadge) {
              wishlistBadge.textContent = data.wishlistCount;
            }
          }
        })
        .catch(error => {
          console.error('Error updating wishlist count:', error);
        });
    }

    // Initialize pagination controls without loading content
    function initializePaginationControls(section) {
      const paginationContainer = document.getElementById(section === 'featured' ? 'featuredPagination' : 'latestPagination');
      if (paginationContainer) {
        // Set up basic pagination for page 1 (server-rendered content)
        paginationContainer.innerHTML = `
          <li class="page-item disabled">
            <a class="page-link" href="#" aria-label="Previous">
              <span aria-hidden="true">«</span>
            </a>
          </li>
          <li class="page-item active">
            <a class="page-link" href="#" data-page="1" data-section="${section}">1</a>
          </li>
          <li class="page-item">
            <a class="page-link" href="#" data-page="2" data-section="${section}" aria-label="Next">
              <span aria-hidden="true">»</span>
            </a>
          </li>
        `;
      }
    }

    // Search form now redirects to shop page directly

    // Event delegation for pagination clicks
    document.addEventListener('click', (event) => {
      const pageLink = event.target.closest('.page-link');
      if (pageLink) {
        event.preventDefault();
        const page = parseInt(pageLink.getAttribute('data-page'));
        const section = pageLink.getAttribute('data-section');
        if (page && section) {
          handlePagination(page, section, false);
        }
      }
    });

    // Handle pagination with AJAX
    async function handlePagination(page, section, isInitialLoad = false) {
      const searchInput = document.getElementById('searchInput');
      const searchQuery = searchInput ? searchInput.value.trim() : '';
      const categorySelect = document.querySelector('select[name="category"]');
      const categoryFilter = categorySelect ? categorySelect.value.trim() : '';

      const params = new URLSearchParams();
      params.append('page', page);
      if (searchQuery) params.append('search', searchQuery);
      if (categoryFilter) params.append('category', categoryFilter);
      params.append('section', section);

      const url = '/home?' + params.toString();
      console.log(`Pagination URL for ${section}:`, url);

      Swal.fire({
        title: 'Loading page...',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
          }
        });
        console.log(`Pagination Response Status for ${section}:`, response.status, `Content-Type:`, response.headers.get('Content-Type'));
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP error! Status: ${response.status}, Response: ${errorText.slice(0, 50)}...`);
        }
        if (response.headers.get('Content-Type')?.includes('text/html')) {
          const text = await response.text();
          throw new Error(`Expected JSON, received HTML: ${text.slice(0, 50)}...`);
        }
        const data = await response.json();
        console.log(`Pagination Response Data for ${section}:`, data);

        if (!data || typeof data !== 'object') {
          throw new Error('Invalid response format');
        }
        if (data.success) {
          updateProductGrid(data, section);
          updatePaginationControls(data, section);
          if (!isInitialLoad) {
            Swal.fire({
              icon: 'success',
              title: 'Page Loaded!',
              text: `Loaded page ${data.currentPage} successfully.`,
              confirmButtonColor: '#6200ea',
              timer: 1500,
              timerProgressBar: true
            });
          } else {
            Swal.close(); // Close the loading popup on initial load
          }
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: data.message || 'Failed to load page.',
            confirmButtonColor: '#6200ea'
          });
        }
      } catch (error) {
        console.error(`Pagination Fetch Error for ${section}:`, error.message, error.stack);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to load products. The server returned an unexpected response. Please try again later.',
          confirmButtonColor: '#6200ea'
        });
        throw error;
      }
    }

    // Function to update the product grid dynamically
    function updateProductGrid(data, section) {
      const productGrid = document.getElementById(section === 'featured' ? 'featuredProductGrid' : 'latestProductGrid');

      if (!productGrid) {
        console.error(`Product grid for ${section} not found`);
        return;
      }

      // Validate data
      if (!data.products || !Array.isArray(data.products)) {
        console.error(`Invalid products data for ${section}:`, data.products);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Invalid product data received.',
          confirmButtonColor: '#6200ea'
        });
        return;
      }

      // Update product grid
      productGrid.innerHTML = '';
      if (data.products.length === 0) {
        productGrid.innerHTML = '<div class="col-12 text-center"><p>No products available at the moment.</p></div>';
        Swal.fire({
          icon: 'info',
          title: 'No Products',
          text: 'No products found.',
          confirmButtonColor: '#6200ea'
        });
      } else {
        data.products.forEach(product => {
          const productCard = `
            <div class="col-md-3">
              <div class="card product-card" style="border: none; box-shadow: 0 4px 8px rgba(0,0,0,0.1); transition: transform 0.3s, box-shadow 0.3s; margin-bottom: 1.5rem;">
                <a href="/product/${product._id}" class="text-decoration-none">
                  <div class="product-thumb" style="height: 200px; overflow: hidden;">
                    ${product.productImage && product.productImage.length > 0
                      ? `<img src="/Uploads/product-images/${product.productImage[0]}" alt="${product.productName}" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s;" />`
                      : `<img src="/images/default-product.jpg" alt="No image" style="width: 100%; height: 100%; object-fit: cover; transition: transform 0.5s;" />`}
                  </div>
                  <div class="product-info" style="padding: 1.5rem;">
                    <h5 class="product-title" style="font-weight: 600; margin-bottom: 0.5rem; color: #333;">${product.productName}</h5>
                    <p class="product-category text-muted" style="font-size: 0.9rem; margin-bottom: 0.5rem;">${typeof product.category === 'string' ? product.category : (product.category ? product.category.name : "Uncategorized")}</p>
                    <div class="price-section">
                      ${product.salePrice && product.salePrice < product.price
                        ? `<div class="d-flex align-items-center flex-wrap">
                             <span class="sale-price text-success fw-bold fs-5">₹${product.salePrice.toFixed(2)}</span>
                             <span class="original-price text-muted text-decoration-line-through ms-2" style="font-size: 0.9rem;">₹${product.price.toFixed(2)}</span>
                           </div>
                           ${product.discount > 0
                             ? `<div class="mt-1"><span class="discount-badge bg-danger text-white px-2 py-1 rounded" style="font-size: 0.7rem; font-weight: bold;">-${product.discount}% OFF</span></div>`
                             : ''}
                           <div class="savings-text mt-1">
                             <small class="text-success fw-bold">Save ₹${(product.price - product.salePrice).toFixed(2)}</small>
                           </div>`
                        : `<span class="product-price text-primary fw-bold" style="font-size: 1.2rem;">₹${product.price.toFixed(2)}</span>`}
                    </div>
                  </div>
                </a>
              </div>
            </div>
          `;
          productGrid.innerHTML += productCard;
        });
      }
    }

    // Function to update pagination controls
    function updatePaginationControls(data, section) {
      const paginationContainer = document.getElementById(section === 'featured' ? 'featuredPagination' : 'latestPagination');

      if (!paginationContainer) {
        console.error(`Pagination container for ${section} not found`);
        return;
      }

      paginationContainer.innerHTML = '';

      // Previous button
      paginationContainer.innerHTML += `
        <li class="page-item ${data.currentPage === 1 ? 'disabled' : ''}">
          <a class="page-link" href="#" data-page="${data.currentPage - 1}" data-section="${section}" aria-label="Previous">
            <span aria-hidden="true">«</span>
          </a>
        </li>
      `;

      // Page numbers
      const maxPagesToShow = 5;
      let startPage = Math.max(1, data.currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = Math.min(data.totalPages, startPage + maxPagesToShow - 1);

      if (endPage - startPage + 1 < maxPagesToShow) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        paginationContainer.innerHTML += `
          <li class="page-item ${data.currentPage === i ? 'active' : ''}">
            <a class="page-link" href="#" data-page="${i}" data-section="${section}">${i}</a>
          </li>
        `;
      }

      // Next button
      paginationContainer.innerHTML += `
        <li class="page-item ${data.currentPage === data.totalPages ? 'disabled' : ''}">
          <a class="page-link" href="#" data-page="${data.currentPage + 1}" data-section="${section}" aria-label="Next">
            <span aria-hidden="true">»</span>
          </a>
        </li>
      `;
    }

    // Add to wishlist function
    function addToWishlist(productId) {
      fetch('/wishlist/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: productId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          Swal.fire({
            icon: 'success',
            title: 'Added to Wishlist!',
            text: data.message,
            confirmButtonColor: '#6200ea',
            timer: 2000,
            timerProgressBar: true
          });

          // Update wishlist count in header
          updateCounts();
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: data.message,
            confirmButtonColor: '#6200ea'
          });
        }
      })
      .catch(error => {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to add item to wishlist',
          confirmButtonColor: '#6200ea'
        });
      });
    }

    function showWishlist() {
      window.location.href = '/wishlist';
    }

    function showCart() {
      window.location.href = '/cart';
    }

    function confirmLogout(event) {
      event.preventDefault();
      Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to log out?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#6200ea',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, log out!'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = '/logout';
        }
      });
    }
  </script>
</body>
</html>
```